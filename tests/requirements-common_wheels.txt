# Create local setuptools wheel files for testing by:
# 1. Cloning setuptools and checking out the branch of interest
# 2. Running `python3 bootstrap.py` in that directory
# 3. Running `python3 -m pip wheel --no-cache -w /tmp/setuptools_build_meta_legacy/ .`
# 4. Replacing the `setuptools` entry below with a `file:///...` URL
# (Adjust artifact directory used based on preference and operating system)

# We pin setuptools<80 because our test suite currently
# depends on setup.py develop to generate egg-link files.
setuptools >= 40.8.0, != 60.6.0, <80
wheel
# As required by pytest-cov.
coverage >= 4.4
