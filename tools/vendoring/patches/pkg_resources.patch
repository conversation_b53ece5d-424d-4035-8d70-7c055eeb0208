diff --git a/src/pip/_vendor/pkg_resources/__init__.py b/src/pip/_vendor/pkg_resources/__init__.py
index d47df3f3c..415c0c432 100644
--- a/src/pip/_vendor/pkg_resources/__init__.py
+++ b/src/pip/_vendor/pkg_resources/__init__.py
@@ -87,7 +87,7 @@ except ImportError:
     # no write support, probably under GAE
     WRITE_SUPPORT = False
 
-from pkg_resources.extern.jaraco.text import (
+from pip._internal.utils._jaraco_text import (
     yield_lines,
     drop_comment,
     join_continuation,
@@ -102,12 +102,11 @@ if TYPE_CHECKING:
     from _typeshed import BytesPath, StrPath, StrOrBytesPath
     from typing_extensions import Self
 
-warnings.warn(
-    "pkg_resources is deprecated as an API. "
-    "See https://setuptools.pypa.io/en/latest/pkg_resources.html",
-    DeprecationWarning,
-    stacklevel=2,
-)
+
+# Patch: Remove deprecation warning from vendored pkg_resources.
+# Setting PYTHONWARNINGS=error to verify builds produce no warnings
+# causes immediate exceptions.
+# See https://github.com/pypa/pip/issues/12243
 
 
 _T = TypeVar("_T")
