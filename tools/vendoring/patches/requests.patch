diff --git a/src/pip/_vendor/requests/__init__.py b/src/pip/_vendor/requests/__init__.py
index 300a16c5..a66f6024 100644
--- a/src/pip/_vendor/requests/__init__.py
+++ b/src/pip/_vendor/requests/__init__.py
@@ -49,10 +49,7 @@ try:
 except ImportError:
     charset_normalizer_version = None
 
-try:
-    from chardet import __version__ as chardet_version
-except ImportError:
-    chardet_version = None
+chardet_version = None
 
 
 def check_compatibility(urllib3_version, chardet_version, charset_normalizer_version):
@@ -76,11 +76,8 @@ def check_compatibility(urllib3_version, chardet_version, charset_normalizer_ver
         # charset_normalizer >= 2.0.0 < 4.0.0
         assert (2, 0, 0) <= (major, minor, patch) < (4, 0, 0)
     else:
-        warnings.warn(
-            "Unable to find acceptable character detection dependency "
-            "(chardet or charset_normalizer).",
-            RequestsDependencyWarning,
-        )
+        # pip does not need or use character detection
+        pass


 def _check_cryptography(cryptography_version):
@@ -118,6 +115,11 @@ except (AssertionError, ValueError):
 # if the standard library doesn't support SNI or the
 # 'ssl' library isn't available.
 try:
+    # Note: This logic prevents upgrading cryptography on Windows, if imported
+    #       as part of pip.
+    from pip._internal.utils.compat import WINDOWS
+    if not WINDOWS:
+        raise ImportError("pip internals: don't import cryptography on Windows")
     try:
         import ssl
     except ImportError:
diff --git a/src/pip/_vendor/requests/compat.py b/src/pip/_vendor/requests/compat.py
index 6776163c..7819bb99 100644
--- a/src/pip/_vendor/requests/compat.py
+++ b/src/pip/_vendor/requests/compat.py
@@ -27,19 +24,10 @@ is_py2 = _ver[0] == 2
 #: Python 3.x?
 is_py3 = _ver[0] == 3
 
-# json/simplejson module import resolution
-has_simplejson = False
-try:
-    import simplejson as json
-
-    has_simplejson = True
-except ImportError:
-    import json
-
-if has_simplejson:
-    from simplejson import JSONDecodeError
-else:
-    from json import JSONDecodeError
+# Note: We've patched out simplejson support in pip because it prevents
+#       upgrading simplejson on Windows.
+import json
+from json import JSONDecodeError
 
 # Keep OrderedDict for backwards compatibility.
 from collections import OrderedDict
diff --git a/src/pip/_vendor/requests/help.py b/src/pip/_vendor/requests/help.py
index 8fbcd656..094e2046 100644
--- a/src/pip/_vendor/requests/help.py
+++ b/src/pip/_vendor/requests/help.py
@@ -15,10 +15,7 @@ try:
 except ImportError:
     charset_normalizer = None
 
-try:
-    import chardet
-except ImportError:
-    chardet = None
+chardet = None
 
 try:
     from urllib3.contrib import pyopenssl
diff --git a/src/pip/_vendor/requests/__init__.py b/src/pip/_vendor/requests/__init__.py
index 9d4e72c60..04230fc8d 100644
--- a/src/pip/_vendor/requests/__init__.py
+++ b/src/pip/_vendor/requests/__init__.py
@@ -44,11 +44,7 @@ from pip._vendor import urllib3

 from .exceptions import RequestsDependencyWarning

-try:
-    from charset_normalizer import __version__ as charset_normalizer_version
-except ImportError:
-    charset_normalizer_version = None
-
+charset_normalizer_version = None
 chardet_version = None


diff --git a/src/pip/_vendor/requests/help.py b/src/pip/_vendor/requests/help.py
index 17ca75eda..ddbb6150d 100644
--- a/src/pip/_vendor/requests/help.py
+++ b/src/pip/_vendor/requests/help.py
@@ -10,11 +10,7 @@ from pip._vendor import urllib3

 from . import __version__ as requests_version

-try:
-    import charset_normalizer
-except ImportError:
-    charset_normalizer = None
-
+charset_normalizer = None
 chardet = None

 try:
--- a/src/pip/_vendor/requests/compat.py
+++ b/src/pip/_vendor/requests/compat.py
@@ -7,7 +7,6 @@ between Python 2 and Python 3. It remains for backwards
 compatibility until the next major version.
 """

-import importlib
 import sys

 # -------------------
@@ -18,12 +17,6 @@ import sys
 def _resolve_char_detection():
     """Find supported character detection libraries."""
     chardet = None
-    for lib in ("chardet", "charset_normalizer"):
-        if chardet is None:
-            try:
-                chardet = importlib.import_module(lib)
-            except ImportError:
-                pass
     return chardet


diff --git a/src/pip/_vendor/requests/packages.py b/src/pip/_vendor/requests/packages.py
index 5ab3d8e25..200c38287 100644
--- a/src/pip/_vendor/requests/packages.py
+++ b/src/pip/_vendor/requests/packages.py
@@ -6,12 +6,14 @@ from .compat import chardet
 # I don't like it either. Just look the other way. :)

 for package in ("urllib3", "idna"):
-    locals()[package] = __import__(package)
+    vendored_package = "pip._vendor." + package
+    locals()[package] = __import__(vendored_package)
     # This traversal is apparently necessary such that the identities are
     # preserved (requests.packages.urllib3.* is urllib3.*)
     for mod in list(sys.modules):
-        if mod == package or mod.startswith(f"{package}."):
-            sys.modules[f"requests.packages.{mod}"] = sys.modules[mod]
+        if mod == vendored_package or mod.startswith(vendored_package + '.'):
+            unprefixed_mod = mod[len("pip._vendor."):]
+            sys.modules['pip._vendor.requests.packages.' + unprefixed_mod] = sys.modules[mod]

 if chardet is not None:
     target = chardet.__name__
