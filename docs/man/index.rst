:orphan:

========
Man Page
========

SYNOPSIS
********

pip <command> [options]

DESCRIPTION
***********

pip is the PyPA recommended package manager for Python packages

OPTIONS
*******

.. pip-general-options::

COMMANDS
********

pip-install(1)
    Install packages.

pip-download(1)
    Download packages.

pip-uninstall(1)
    Uninstall packages.

pip-freeze(1)
    Output installed packages in requirements format.

pip-lock(1)
    Generate a lock file for requirements and their dependencies.

pip-list(1)
    List installed packages.

pip-show(1)
    Show information about installed packages.

pip-check(1)
    Verify installed packages have compatible dependencies.

pip-search(1)
    Search PyPI for packages.

pip-wheel(1)
    Build wheels from your requirements.

pip-hash(1)
    Compute hashes of package archives.

pip-help(1)
    Show help for pip commands.
