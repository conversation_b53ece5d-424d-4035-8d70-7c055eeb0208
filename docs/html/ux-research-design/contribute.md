# How to Contribute

## Participate in UX Research

It is important that we hear from pip users so that we can:

- Understand how pip is currently used by the Python community
- Understand how pip users _need_ pip to behave
- Understand how pip users _would like_ pip to behave
- Understand pip’s strengths and shortcomings
- Make useful design recommendations for improving pip

If you are interested in participating in pip user research, please [join pip’s user panel](https://mail.python.org/mailman3/lists/pip-ux-studies.python.org/).

## Test New Features

You can help the team by testing new features as they are released to the community.

## Report and Work on UX Issues

If you believe that you have found a user experience bug in pip, or you have ideas for how pip could be made better for all users, please file an issue on the [pip issue tracker](https://github.com/pypa/pip/issues/new).

You can also help improve pip’s user experience by [working on UX issues](https://github.com/pypa/pip/issues?q=is%3Aissue+label%3AUX+is%3Aopen). Issues that are ideal for new contributors are marked with “[good first issue](https://github.com/pypa/pip/issues?q=is%3Aopen+is%3Aissue+label%3A%22good+first+issue%22)”. Explore the
[UX Guidance](guidance.md) if you have questions.
