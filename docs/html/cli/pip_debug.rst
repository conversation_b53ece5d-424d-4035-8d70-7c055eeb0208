.. _`pip debug`:

=========
pip debug
=========


Usage
=====

.. tab:: Unix/macOS

   .. pip-command-usage:: debug "python -m pip"

.. tab:: Windows

   .. pip-command-usage:: debug "py -m pip"


.. warning::

    This command is only meant for debugging.
    Its options and outputs are provisional and may change without notice.


Description
===========

.. pip-command-description:: debug


Options
=======

.. pip-command-options:: debug
