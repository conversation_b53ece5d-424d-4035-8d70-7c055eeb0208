.. _`pip inspect`:

===========
pip inspect
===========

.. versionadded:: 22.2


Usage
=====

.. tab:: Unix/macOS

   .. pip-command-usage:: inspect "python -m pip"

.. tab:: Windows

   .. pip-command-usage:: inspect "py -m pip"


Description
===========

.. pip-command-description:: inspect

The format of the JSON output is described in :doc:`../reference/inspect-report`.


Options
=======

.. pip-command-options:: inspect
