---
hide-toc: true
---

# pip

pip is the [package installer for Python][recommended]. You can use it to
install packages from the [Python Package Index][pypi] and other indexes.

```{toctree}
:hidden:

getting-started
installation
user_guide
topics/index
reference/index
cli/index
```

```{toctree}
:caption: Project
:hidden:

development/index
ux-research-design/index
news
Code of Conduct <https://github.com/pypa/.github/blob/main/CODE_OF_CONDUCT.md>
GitHub <https://github.com/pypa/pip>
```

If you want to learn about how to use pip, check out the following resources:

- [Getting Started](getting-started)
- [Python Packaging User Guide](https://packaging.python.org)

If you find bugs, need help, or want to talk to the developers, use our mailing
lists or chat rooms:

- [GitHub Issues][issue-tracker]
- [Discourse channel][packaging-discourse]
- [User IRC][irc-pypa]
- [Development IRC][irc-pypa-dev]

[recommended]: https://packaging.python.org/guides/tool-recommendations/
[pypi]: https://pypi.org/
[issue-tracker]: https://github.com/pypa/pip/issues/
[packaging-discourse]: https://discuss.python.org/c/packaging/14
[irc-pypa]: https://kiwiirc.com/nextclient/#ircs://irc.libera.chat:+6697/pypa
[irc-pypa-dev]: https://kiwiirc.com/nextclient/#ircs://irc.libera.chat:+6697/pypa-dev

If you find any security issues, please report to [<EMAIL>](mailto:<EMAIL>)
