.. _architecture-pip-internals:

===============================
Architecture of pip's internals
===============================

.. note::

    This section of the documentation is currently being written. pip
    developers welcome your help to complete this documentation. If
    you're interested in helping out, please let us know in the
    `tracking issue`_, or just go ahead and submit a pull request and
    mention it in that tracking issue.

.. note::

    Direct use of pip's internals is *not supported*, and these internals
    can change at any time. For more details, see :ref:`Using pip from
    your program`.


.. toctree::
    :maxdepth: 2

    overview
    anatomy
    configuration-files
    package-finding
    command-line-interface
    upgrade-options


.. _`tracking issue`: https://github.com/pypa/pip/issues/6831
