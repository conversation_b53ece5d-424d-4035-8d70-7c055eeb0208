===========
Development
===========

pip is a volunteer maintained open source project and we welcome contributions
of all forms. The sections below will help you get started with development,
testing, and documentation.

You can also join ``#pypa`` (general packaging discussion and user support) and
``#pypa-dev`` (discussion about development of packaging tools) `on Libera.chat`_,
or the ``#pip`` channel on the `PyPA Discord`_, to ask questions or get involved.

.. toctree::
    :maxdepth: 2

    getting-started
    contributing
    ci
    issue-triage
    architecture/index
    release-process
    vendoring-policy

.. note::

    pip's development documentation has been rearranged and some older
    references might be broken.

.. _`on Libera.chat`: https://kiwiirc.com/nextclient/#ircs://irc.libera.chat:+6697/pypa-dev
.. _`PyPA Discord`: https://discord.gg/pypa
