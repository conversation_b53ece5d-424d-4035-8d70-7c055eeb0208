# `pip inspect` JSON output specification

```{versionadded} 22.2
```

```{versionchanged} 23.0
``version`` has been bumped to ``1`` and the format declared stable.
```

The `pip inspect` command produces a detailed JSON report of the Python
environment, including installed distributions.

## Specification

The report is a JSON object with the following properties:

- `version`: the string `1`. It will change only if
  and when backward incompatible changes are introduced, such as removing mandatory
  fields or changing the semantics or data type of existing fields. The introduction of
  backward incompatible changes will follow the usual pip processes such as the
  deprecation cycle or feature flags. Tools must check this field to ensure they support
  the corresponding version.

- `pip_version`: a string with the version of pip used to produce the report.

- `installed`: an array of [`InspectReportItem`](InspectReportItem) representing the
  distribution packages that are installed.

- `environment`: an object describing the environment where the installation report was
  generated. See the section on environment markers in the {ref}`pypug:dependency-specifiers`
  specification for more information. Values have a string type.

(InspectReportItem)=

An `InspectReportItem` is an object describing an installed distribution package with
the following properties:

- `metadata`: the metadata of the distribution, converted to a JSON object according to
  the [PEP 566
  transformation](https://www.python.org/dev/peps/pep-0566/#json-compatible-metadata).

- `metadata_location`: the location of the metadata of the installed distribution. Most
  of the time this is the `.dist-info` directory. For legacy installs it is the
  `.egg-info` directory.

  ```{warning}
  This field may not necessarily point to a directory, for instance, in the case of older
  `.egg` installs.
  ```

- `direct_url`: Information about the direct URL that was used for installation, if any,
  using the [direct URL data
  structure](https://packaging.python.org/en/latest/specifications/direct-url-data-structure/).
  In most case, this field corresponds to the
  [`direct_url.json`](https://packaging.python.org/en/latest/specifications/direct-url)
  metadata, except for legacy editable installs, where it is emulated.

- `requested`: `true` if the `REQUESTED` metadata is present, `false` otherwise. This
  field is only present for modern `.dist-info` installations.

  ```{note}
  The `REQUESTED` metadata may not be generated by all installers.
  It is generated by pip since version 20.2.
  ```

- `installer`: the content of the `INSTALLER` metadata, if present and not empty.

## Example

Running the ``pip inspect`` command, in an environment where `pip` is installed in
editable mode and `packaging` is installed as well, will produce an output similar to
this (metadata abriged for brevity):

```json
{
  "version": "1",
  "pip_version": "22.2.dev0",
  "installed": [
    {
      "metadata": {
        "metadata_version": "2.1",
        "name": "pyparsing",
        "version": "3.0.9",
        "summary": "pyparsing module - Classes and methods to define and execute parsing grammars",
        "description_content_type": "text/x-rst",
        "author_email": "Paul McGuire <<EMAIL>>",
        "classifier": [
          "Development Status :: 5 - Production/Stable",
          "Intended Audience :: Developers",
          "Intended Audience :: Information Technology",
          "License :: OSI Approved :: MIT License",
          "Operating System :: OS Independent",
          "Programming Language :: Python",
          "Programming Language :: Python :: 3",
          "Programming Language :: Python :: 3.6",
          "Programming Language :: Python :: 3.7",
          "Programming Language :: Python :: 3.8",
          "Programming Language :: Python :: 3.9",
          "Programming Language :: Python :: 3.10",
          "Programming Language :: Python :: 3 :: Only",
          "Programming Language :: Python :: Implementation :: CPython",
          "Programming Language :: Python :: Implementation :: PyPy",
          "Typing :: Typed"
        ],
        "requires_dist": [
          "railroad-diagrams ; extra == \"diagrams\"",
          "jinja2 ; extra == \"diagrams\""
        ],
        "requires_python": ">=3.6.8",
        "project_url": [
          "Homepage, https://github.com/pyparsing/pyparsing/"
        ],
        "provides_extra": [
          "diagrams"
        ],
        "description": "..."
      },
      "metadata_location": "/home/<USER>/.virtualenvs/demoenv/lib/python3.8/site-packages/pyparsing-3.0.9.dist-info",
      "installer": "pip",
      "requested": false
    },
    {
      "metadata": {
        "metadata_version": "2.1",
        "name": "packaging",
        "version": "21.3",
        "platform": [
          "UNKNOWN"
        ],
        "summary": "Core utilities for Python packages",
        "description_content_type": "text/x-rst",
        "home_page": "https://github.com/pypa/packaging",
        "author": "Donald Stufft and individual contributors",
        "author_email": "<EMAIL>",
        "license": "BSD-2-Clause or Apache-2.0",
        "classifier": [
          "Development Status :: 5 - Production/Stable",
          "Intended Audience :: Developers",
          "License :: OSI Approved :: Apache Software License",
          "License :: OSI Approved :: BSD License",
          "Programming Language :: Python",
          "Programming Language :: Python :: 3",
          "Programming Language :: Python :: 3 :: Only",
          "Programming Language :: Python :: 3.6",
          "Programming Language :: Python :: 3.7",
          "Programming Language :: Python :: 3.8",
          "Programming Language :: Python :: 3.9",
          "Programming Language :: Python :: 3.10",
          "Programming Language :: Python :: Implementation :: CPython",
          "Programming Language :: Python :: Implementation :: PyPy"
        ],
        "requires_dist": [
          "pyparsing (!=3.0.5,>=2.0.2)"
        ],
        "requires_python": ">=3.6",
        "description": "..."
      },
      "metadata_location": "/home/<USER>/.virtualenvs/demoenv/lib/python3.8/site-packages/packaging-21.3.dist-info",
      "installer": "pip",
      "requested": true
    },
    {
      "metadata": {
        "metadata_version": "2.1",
        "name": "pip",
        "version": "22.2.dev0",
        "summary": "The PyPA recommended tool for installing Python packages.",
        "home_page": "https://pip.pypa.io/",
        "author": "The pip developers",
        "author_email": "<EMAIL>",
        "license": "MIT",
        "classifier": [
          "Development Status :: 5 - Production/Stable",
          "Intended Audience :: Developers",
          "License :: OSI Approved :: MIT License",
          "Topic :: Software Development :: Build Tools",
          "Programming Language :: Python",
          "Programming Language :: Python :: 3",
          "Programming Language :: Python :: 3 :: Only",
          "Programming Language :: Python :: 3.7",
          "Programming Language :: Python :: 3.8",
          "Programming Language :: Python :: 3.9",
          "Programming Language :: Python :: 3.10",
          "Programming Language :: Python :: Implementation :: CPython",
          "Programming Language :: Python :: Implementation :: PyPy"
        ],
        "requires_python": ">=3.7",
        "project_url": [
          "Documentation, https://pip.pypa.io",
          "Source, https://github.com/pypa/pip",
          "Changelog, https://pip.pypa.io/en/stable/news/"
        ],
        "description": "..."
      },
      "metadata_location": "/home/<USER>/pip/src/pip.egg-info",
      "direct_url": {
        "url": "file:///home/<USER>/pip/src",
        "dir_info": {
          "editable": true
        }
      }
    }
  ],
  "environment": {
    "implementation_name": "cpython",
    "implementation_version": "3.8.10",
    "os_name": "posix",
    "platform_machine": "x86_64",
    "platform_release": "5.13-generic",
    "platform_system": "Linux",
    "platform_version": "...",
    "python_full_version": "3.8.10",
    "platform_python_implementation": "CPython",
    "python_version": "3.8",
    "sys_platform": "linux"
  }
}
```
